Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
ChatbotModal.tsx:70 🚪 Modal fechado, resetando flag...
AuthContext.tsx:44 🔍 Buscando funcionário por user_id: dd413244-9059-467e-b8c9-ea0f0a7338b6 email: <EMAIL>
AuthContext.tsx:96 ✅ Funcionário encontrado: {id: '48de2d34-0fed-4dc5-b965-4cfed3626569', nome: 'ODTWIN FRITSCHE FH', email: '<EMAIL>', telefone: '(61) 99999-9999', nivel_acesso: 'admin', …}
ChatbotModal.tsx:53 🎯 Modal aberto, iniciando conversa única...
useChatbotConversation.ts:615 🔄 Resetando conversa...
useChatbotConversation.ts:639 ✅ Reset completo finalizado
@radix-ui_react-dialog.js?v=c340e569:341 Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anonymous) @ @radix-ui_react-dialog.js?v=c340e569:341
commitHookEffectListMount @ chunk-FJ2A54M7.js?v=c340e569:16915
commitPassiveMountOnFiber @ chunk-FJ2A54M7.js?v=c340e569:18156
commitPassiveMountEffects_complete @ chunk-FJ2A54M7.js?v=c340e569:18129
commitPassiveMountEffects_begin @ chunk-FJ2A54M7.js?v=c340e569:18119
commitPassiveMountEffects @ chunk-FJ2A54M7.js?v=c340e569:18109
flushPassiveEffectsImpl @ chunk-FJ2A54M7.js?v=c340e569:19490
flushPassiveEffects @ chunk-FJ2A54M7.js?v=c340e569:19447
commitRootImpl @ chunk-FJ2A54M7.js?v=c340e569:19416
commitRoot @ chunk-FJ2A54M7.js?v=c340e569:19277
performSyncWorkOnRoot @ chunk-FJ2A54M7.js?v=c340e569:18895
flushSyncCallbacks @ chunk-FJ2A54M7.js?v=c340e569:9119
(anonymous) @ chunk-FJ2A54M7.js?v=c340e569:18627Understand this warning
ChatbotModal.tsx:62 ⏰ Timeout executado, chamando startConversation...
useChatbotConversation.ts:569 🚀 startConversation chamada
useChatbotConversation.ts:585 🔒 Flags de inicialização e adição definidos como true
useChatbotConversation.ts:596 💬 Enviando saudação: "E aí! Assis aqui da AssisMax! 💰 Tenho preços incr..."
useChatbotConversation.ts:115 ⚠️ Já está adicionando mensagem, ignorando duplicata: "E aí! Assis aqui da AssisMax! ..."
useChatbotConversation.ts:606 ✅ Conversa iniciada com sucesso
ChatbotModal.tsx:70 🚪 Modal fechado, resetando flag...
ChatbotModal.tsx:53 🎯 Modal aberto, iniciando conversa única...
useChatbotConversation.ts:615 🔄 Resetando conversa...
useChatbotConversation.ts:639 ✅ Reset completo finalizado
@radix-ui_react-dialog.js?v=c340e569:341 Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anonymous) @ @radix-ui_react-dialog.js?v=c340e569:341
commitHookEffectListMount @ chunk-FJ2A54M7.js?v=c340e569:16915
commitPassiveMountOnFiber @ chunk-FJ2A54M7.js?v=c340e569:18156
commitPassiveMountEffects_complete @ chunk-FJ2A54M7.js?v=c340e569:18129
commitPassiveMountEffects_begin @ chunk-FJ2A54M7.js?v=c340e569:18119
commitPassiveMountEffects @ chunk-FJ2A54M7.js?v=c340e569:18109
flushPassiveEffectsImpl @ chunk-FJ2A54M7.js?v=c340e569:19490
flushPassiveEffects @ chunk-FJ2A54M7.js?v=c340e569:19447
commitRootImpl @ chunk-FJ2A54M7.js?v=c340e569:19416
commitRoot @ chunk-FJ2A54M7.js?v=c340e569:19277
performSyncWorkOnRoot @ chunk-FJ2A54M7.js?v=c340e569:18895
flushSyncCallbacks @ chunk-FJ2A54M7.js?v=c340e569:9119
(anonymous) @ chunk-FJ2A54M7.js?v=c340e569:18627Understand this warning
ChatbotModal.tsx:62 ⏰ Timeout executado, chamando startConversation...
useChatbotConversation.ts:569 🚀 startConversation chamada
useChatbotConversation.ts:585 🔒 Flags de inicialização e adição definidos como true
useChatbotConversation.ts:596 💬 Enviando saudação: "Olá! Prazer, sou o Assis da AssisMax! 😊 Quero te ..."
useChatbotConversation.ts:115 ⚠️ Já está adicionando mensagem, ignorando duplicata: "Olá! Prazer, sou o Assis da As..."
useChatbotConversation.ts:606 ✅ Conversa iniciada com sucesso
useChatbotConversation.ts:615 🔄 Resetando conversa...
useChatbotConversation.ts:639 ✅ Reset completo finalizado
useChatbotConversation.ts:569 🚀 startConversation chamada
useChatbotConversation.ts:585 🔒 Flags de inicialização e adição definidos como true
useChatbotConversation.ts:596 💬 Enviando saudação: "Olá! Prazer, sou o Assis da AssisMax! 😊 Quero te ..."
useChatbotConversation.ts:115 ⚠️ Já está adicionando mensagem, ignorando duplicata: "Olá! Prazer, sou o Assis da As..."
useChatbotConversation.ts:606 ✅ Conversa iniciada com sucesso
useChatbotConversation.ts:615 🔄 Resetando conversa...
useChatbotConversation.ts:639 ✅ Reset completo finalizado
useChatbotConversation.ts:569 🚀 startConversation chamada
useChatbotConversation.ts:585 🔒 Flags de inicialização e adição definidos como true
useChatbotConversation.ts:596 💬 Enviando saudação: "Oi! Eu sou o Assis, dono da AssisMax! 👋 Vou te aj..."
useChatbotConversation.ts:115 ⚠️ Já está adicionando mensagem, ignorando duplicata: "Oi! Eu sou o Assis, dono da As..."
useChatbotConversation.ts:606 ✅ Conversa iniciada com sucesso
useChatbotConversation.ts:615 🔄 Resetando conversa...
useChatbotConversation.ts:639 ✅ Reset completo finalizado
useChatbotConversation.ts:569 🚀 startConversation chamada
useChatbotConversation.ts:585 🔒 Flags de inicialização e adição definidos como true
useChatbotConversation.ts:596 💬 Enviando saudação: "E aí! Assis aqui da AssisMax! 💰 Tenho preços incr..."
useChatbotConversation.ts:115 ⚠️ Já está adicionando mensagem, ignorando duplicata: "E aí! Assis aqui da AssisMax! ..."
useChatbotConversation.ts:606 ✅ Conversa iniciada com sucesso
useChatbotConversation.ts:615 🔄 Resetando conversa...
useChatbotConversation.ts:639 ✅ Reset completo finalizado
useChatbotConversation.ts:569 🚀 startConversation chamada
useChatbotConversation.ts:585 🔒 Flags de inicialização e adição definidos como true
useChatbotConversation.ts:596 💬 Enviando saudação: "Oi! Eu sou o Assis, dono da AssisMax! 👋 Vou te aj..."
useChatbotConversation.ts:115 ⚠️ Já está adicionando mensagem, ignorando duplicata: "Oi! Eu sou o Assis, dono da As..."
useChatbotConversation.ts:606 ✅ Conversa iniciada com sucesso