Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
ChatbotModal.tsx:70 🚪 Modal fechado, resetando flag...
AuthContext.tsx:44 🔍 Buscando funcionário por user_id: dd413244-9059-467e-b8c9-ea0f0a7338b6 email: <EMAIL>
AuthContext.tsx:96 ✅ Funcionário encontrado: Object
ChatbotModal.tsx:53 🎯 Modal aberto, iniciando conversa única...
useChatbotConversation.ts:601 🔄 Resetando conversa...
chunk-FJ2A54M7.js?v=c340e569:16670 Uncaught ReferenceError: addingMessage is not defined
    at useChatbotConversation.ts:603:5
    at ChatbotModal.tsx:57:7
    at commitHookEffectListMount (chunk-FJ2A54M7.js?v=c340e569:16915:34)
    at commitPassiveMountOnFiber (chunk-FJ2A54M7.js?v=c340e569:18156:19)
    at commitPassiveMountEffects_complete (chunk-FJ2A54M7.js?v=c340e569:18129:17)
    at commitPassiveMountEffects_begin (chunk-FJ2A54M7.js?v=c340e569:18119:15)
    at commitPassiveMountEffects (chunk-FJ2A54M7.js?v=c340e569:18109:11)
    at flushPassiveEffectsImpl (chunk-FJ2A54M7.js?v=c340e569:19490:11)
    at flushPassiveEffects (chunk-FJ2A54M7.js?v=c340e569:19447:22)
    at commitRootImpl (chunk-FJ2A54M7.js?v=c340e569:19416:13)Understand this error
chunk-FJ2A54M7.js?v=c340e569:14032 The above error occurred in the <ChatbotModal> component:

    at ChatbotModal (http://localhost:8081/src/components/ChatbotModal.tsx:31:40)
    at div
    at Index (http://localhost:8081/src/pages/Index.tsx:31:43)
    at RenderedRoute (http://localhost:8081/node_modules/.vite/deps/react-router-dom.js?v=c340e569:4088:5)
    at Routes (http://localhost:8081/node_modules/.vite/deps/react-router-dom.js?v=c340e569:4558:5)
    at AuthProvider (http://localhost:8081/src/contexts/AuthContext.tsx:27:32)
    at Router (http://localhost:8081/node_modules/.vite/deps/react-router-dom.js?v=c340e569:4501:15)
    at BrowserRouter (http://localhost:8081/node_modules/.vite/deps/react-router-dom.js?v=c340e569:5247:5)
    at Provider (http://localhost:8081/node_modules/.vite/deps/chunk-NISJXOOQ.js?v=c340e569:48:15)
    at TooltipProvider (http://localhost:8081/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=c340e569:62:5)
    at QueryClientProvider (http://localhost:8081/node_modules/.vite/deps/@tanstack_react-query.js?v=c340e569:2934:3)
    at App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
logCapturedError @ chunk-FJ2A54M7.js?v=c340e569:14032Understand this error
chunk-FJ2A54M7.js?v=c340e569:9129 Uncaught ReferenceError: addingMessage is not defined
    at useChatbotConversation.ts:603:5
    at ChatbotModal.tsx:57:7
    at commitHookEffectListMount (chunk-FJ2A54M7.js?v=c340e569:16915:34)
    at commitPassiveMountOnFiber (chunk-FJ2A54M7.js?v=c340e569:18156:19)
    at commitPassiveMountEffects_complete (chunk-FJ2A54M7.js?v=c340e569:18129:17)
    at commitPassiveMountEffects_begin (chunk-FJ2A54M7.js?v=c340e569:18119:15)
    at commitPassiveMountEffects (chunk-FJ2A54M7.js?v=c340e569:18109:11)
    at flushPassiveEffectsImpl (chunk-FJ2A54M7.js?v=c340e569:19490:11)
    at flushPassiveEffects (chunk-FJ2A54M7.js?v=c340e569:19447:22)
    at commitRootImpl (chunk-FJ2A54M7.js?v=c340e569:19416:13)Understand this error